#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vienna商标分类数据清洗工具 - Vienna分类数据爬取和入库
支持从WIPO官网获取1-29类Vienna分类数据并清洗入库
"""

import requests
import re
import time
import logging
import pymysql
from bs4 import BeautifulSoup
from typing import List, Dict, Optional, Tuple, Callable, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlencode
import json
import os
from datetime import datetime
from functools import wraps

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vienna_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def network_retry(max_retries: int = 3, base_delay: float = 1.0):
    """
    网络请求重试装饰器

    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (requests.RequestException, ConnectionError, TimeoutError) as e:
                    last_exception = e
                    if attempt < max_retries:
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        logger.warning(f"请求失败，{delay}秒后重试 (第{attempt + 1}次): {e}")
                        time.sleep(delay)
                    else:
                        logger.error(f"请求失败，已达最大重试次数: {e}")

            raise last_exception
        return wrapper
    return decorator


@dataclass
class ViennaClassItem:
    """Vienna分类数据项"""
    class_type: str = "VIENNA"  # 固定为VIENNA
    class_id: str = ""  # 分类ID，如 1.1, 1.1.15
    class_level_1: str = ""  # 一级分类，如 1
    class_description_en: str = ""  # 英文描述
    class_description_cn: str = ""  # 中文描述（暂时为空）
    version: str = "10"  # 固定版本号10


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    database: str = "db_trademark_staging"
    charset: str = "utf8mb4"


class ViennaSpider:
    """Vienna分类数据爬虫"""

    def __init__(self, db_config: DatabaseConfig):
        """
        初始化爬虫

        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.base_url = "https://nivilo.wipo.int/vienna10/divrender.htm"
        self.class_range = range(1, 30)  # Vienna分类范围1-29
        self.version = "10"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self._db_connection = None

        # 请求参数模板
        self.request_params = {
            'xml': 'xml/en/full.xml',
            'xsl': 'xslt/addmode.xsl',
            'cxsl': 'xslt/vienna8.xsl',
            'mode': 'full',
            'lang': 'EN'
        }

    def get_db_connection(self):
        """获取数据库连接"""
        if self._db_connection is None or not self._db_connection.open:
            self._db_connection = pymysql.connect(
                host=self.db_config.host,
                port=self.db_config.port,
                user=self.db_config.user,
                password=self.db_config.password,
                database=self.db_config.database,
                charset=self.db_config.charset,
                autocommit=True
            )
        return self._db_connection

    def close_db_connection(self):
        """关闭数据库连接"""
        if self._db_connection and self._db_connection.open:
            self._db_connection.close()
            self._db_connection = None

    @network_retry(max_retries=3, base_delay=2.0)
    def fetch_class_data(self, class_number: int) -> Optional[str]:
        """
        获取指定分类的HTML数据

        Args:
            class_number: 分类号 (1-29)

        Returns:
            HTML内容字符串，失败返回None
        """
        try:
            # 构建请求参数
            params = self.request_params.copy()
            params['index'] = str(class_number)
            params['hash'] = f'id{class_number}'

            logger.info(f"正在获取第{class_number}类Vienna分类数据...")

            response = self.session.get(
                self.base_url,
                params=params,
                timeout=30
            )
            response.raise_for_status()

            # 检查响应内容
            if len(response.text) < 100:
                logger.warning(f"第{class_number}类数据内容过短，可能获取失败")
                return None

            logger.info(f"成功获取第{class_number}类数据，内容长度: {len(response.text)}")
            return response.text

        except Exception as e:
            logger.error(f"获取第{class_number}类数据失败: {e}")
            return None