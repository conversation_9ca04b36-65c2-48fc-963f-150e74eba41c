# Vienna商标分类数据清洗工具

这是一个用于处理Vienna商标分类数据的清洗和入库工具。它可以解析本地的XML文件，提取分类信息，并将数据保存到MySQL数据库中。

## 功能特性

- ✅ 解析Vienna分类XML文件（full.xml）
- ✅ 支持多层级分类（二级、三级、辅助分类）
- ✅ 自动清理HTML标签和注释
- ✅ 批量数据入库，支持重复数据更新
- ✅ 完整的日志记录
- ✅ 命令行参数支持
- ✅ 数据统计功能

## 数据结构

解析的数据包含以下层级：

1. **二级分类**: 如 `1.1` (STARS, COMETS)
2. **三级分类**: 如 `1.1.1` (Stars)
3. **辅助分类**: 如 `1.1.2` (One star)

所有数据都会保存到 `trademark_class_map` 表中，字段包括：
- `class_type`: 固定为 "VIENNA"
- `class_id`: 分类ID（如 1.1, 1.1.1, 1.1.2）
- `class_level_1`: 一级分类号（如 1）
- `class_description_en`: 英文描述
- `class_description_cn`: 中文描述（暂时为空）
- `version`: 版本号（固定为 "10"）

## 安装要求

```bash
pip install pymysql
```

## 数据库表结构

请确保数据库中存在 `trademark_class_map` 表，可以使用以下SQL创建：

```sql
-- 参考 trademark_class_map.ddl.sql 文件
```

## 使用方法

### 1. 基本使用

```bash
# 显示帮助信息
python vienna_spider.py --help

# 使用默认配置运行（需要配置数据库参数）
python vienna_spider.py --host localhost --user root --password your_password --database db_trademark_staging

# 清理现有数据后重新爬取
python vienna_spider.py --clear --host localhost --user root --password your_password

# 仅显示统计信息
python vienna_spider.py --stats --host localhost --user root --password your_password
```

### 2. 测试功能

```bash
# 运行测试脚本
python test_vienna_spider.py
```

测试脚本提供三种测试模式：
1. **仅测试XML解析**：无需数据库连接，测试XML文件解析功能
2. **测试数据库连接**：测试数据库连接和表结构
3. **测试完整流程**：测试完整的解析和入库流程

### 3. 配置文件使用

1. 复制配置模板：
```bash
cp config_template.py config.py
```

2. 修改 `config.py` 中的数据库配置

3. 在代码中使用配置文件（需要修改代码）

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--clear` | 清理现有数据后重新爬取 | False |
| `--stats` | 仅显示统计信息 | False |
| `--host` | 数据库主机地址 | localhost |
| `--port` | 数据库端口 | 3306 |
| `--user` | 数据库用户名 | root |
| `--password` | 数据库密码 | 空 |
| `--database` | 数据库名称 | db_trademark_staging |

## 文件说明

- `vienna_spider.py`: 主爬虫程序
- `test_vienna_spider.py`: 测试脚本
- `config_template.py`: 配置文件模板
- `full.xml`: Vienna分类数据XML文件
- `trademark_class_map.ddl.sql`: 数据库表结构文件
- `README.md`: 说明文档

## 日志

程序运行时会生成详细的日志：
- 控制台输出：实时显示运行状态
- 日志文件：`vienna_spider.log`

## 数据统计示例

```
数据库统计信息:
总记录数: 1925
按版本统计: {'10': 1925}
按层级统计: {'二级分类': 150, '三级分类': 1775}
```

## 注意事项

1. 确保 `full.xml` 文件存在于程序目录中
2. 数据库表 `trademark_class_map` 必须预先创建
3. 程序使用 `ON DUPLICATE KEY UPDATE` 处理重复数据
4. 建议在生产环境运行前先使用测试脚本验证

## 错误处理

程序包含完整的错误处理机制：
- XML解析错误
- 数据库连接错误
- 数据插入错误
- 文件不存在错误

所有错误都会记录到日志中，便于排查问题。
