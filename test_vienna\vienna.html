<html>
    <head>
        <meta http-equiv="Content-Language" content="en-us"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <link name="styles" rel="STYLESHEET" href="nivilo.css" type="text/css">
		<!--[if IE]><link href="ie.css" rel="stylesheet" type="text/css" /><![endif]-->
		<![if !IE]><link href="ff.css" rel="stylesheet" type="text/css" /><![endif]>
		<!--[if IE]><link href="ie.css" rel="stylesheet" type="text/css" /><![endif]-->
		<![if !IE]><link href="ff.css" rel="stylesheet" type="text/css" /><![endif]>
		<link name="styles" rel="STYLESHEET" href="vienna.css" type="text/css">
    </head>

	<script language="javascript" src="config.js"></script>
	<script language="javascript" src="js/global.js"></script>
	<script language="javascript" src="js/common.js"></script>
    <script language="javascript">

		// Copyright by Zoltan Szilagyi (a.k.a. Zedas) 2004
		function transformDataIE(aXSLs, sXML, aParams,fCallback) {
			var oXML = new ActiveXObject('MSXML2.FreeThreadedDOMDocument');
			oXML.async = false;
    		var oRoot = getRoot();

			for (var t = 0; t < aXSLs.length; t++) {
				if (t == 0) {
					sXML = encodeURIComponent(sXML).replace(/%2F/g, '/'); // Repair unicode characters
					oXML.load(sXML);
				} else
					oXML.loadXML(sOut);
/*
				if (t == aXSLs.length -1)
					prompt('', oXML.xml);
*/
				if (oRoot == null) {
		    		var oXSL = new ActiveXObject('MSXML2.FreeThreadedDOMDocument');
				    oXSL.async = false;
				    oXSL.load(aXSLs[t]);
				} else
					var oXSL = oRoot.loadFromCache(aXSLs[t]);

				var oXSLT = new ActiveXObject('MSXML2.XSLTemplate');
				oXSLT.stylesheet = oXSL;
				var oXSLProc = oXSLT.createProcessor();
				oXSLProc.input = oXML;

				if (aParams != null)
					for (var i = 0; i < (aParams.length /2); i++) {
						oXSLProc.addParameter(aParams[i *2], aParams[i *2 +1]);
					}
				oXSLProc.transform();

				sOut = oXSLProc.output;
			}

			document.getElementById('idTarget').innerHTML = sOut;
			fCallback();
		}



	function transformDataIE_Async(aXSLs, sXML, aParams,fCallback) {
			var oXML = new ActiveXObject('MSXML2.FreeThreadedDOMDocument');
			oXML.async = false;
    		var oRoot = getRoot();

			for (var t = 0; t < aXSLs.length; t++) {
				if (t == 0) {
					sXML = encodeURIComponent(sXML).replace(/%2F/g, '/'); // Repair unicode characters
					oXML.load(sXML);
				} else
					oXML.loadXML(sOut);
/*
				if (t == aXSLs.length -1)
					prompt('', oXML.xml);
*/
				if (oRoot == null) {
		    		var oXSL = new ActiveXObject('MSXML2.FreeThreadedDOMDocument');
				    oXSL.async = false;
				    oXSL.load(aXSLs[t]);
				} else
					var oXSL = oRoot.loadFromCache(aXSLs[t]);

				var oXSLT = new ActiveXObject('MSXML2.XSLTemplate');
				oXSLT.stylesheet = oXSL;
				var oXSLProc = oXSLT.createProcessor();
				oXSLProc.input = oXML;

				if (aParams != null)
					for (var i = 0; i < (aParams.length /2); i++) {
						oXSLProc.addParameter(aParams[i *2], aParams[i *2 +1]);
					}
				oXSLProc.transform();

				sOut = oXSLProc.output;
			}

			document.getElementById('idTarget').innerHTML = sOut;
			fCallback();
		}

		function transformDataNS(aXSLs, sXML, aParams, fCallback) {
    		var oHTTPRequest = new XMLHttpRequest();
    		oHTTPRequest.open('GET', sXML, false);
    		oHTTPRequest.send(null);
    		var oXML = oHTTPRequest.responseXML;
     		var oRoot = getRoot();

			for (var t = 0; t < aXSLs.length; t++) {
				if (oRoot == null) {
		    		oHTTPRequest.open('GET', aXSLs[t], false);
		    		oHTTPRequest.send(null);
		    		var oXSL = oHTTPRequest.responseXML;
		    	} else
			    	var oXSL = oRoot.loadFromCache(aXSLs[t]);

	    		var oProc = new XSLTProcessor();
	    		oProc.importStylesheet(oXSL);

				if (aParams != null)
					for (var i = 0; i < (aParams.length /2); i++) {
						oProc.setParameter(null, aParams[i *2], aParams[i *2 +1]);
					}

				if (t < aXSLs.length -1)
					oXML = oProc.transformToDocument(oXML);
				else
					oXML = oProc.transformToFragment(oXML, document);
			}
			document.getElementById('idTarget').appendChild(oXML);
			fCallback();
		}


	function processXSLT_Async(oXML,aXSLs, aParams,t,fCallback)
	{
		var oRoot = getRoot();
		if(t==aXSLs.length ){
			document.getElementById('idTarget').appendChild(oXML);
			fCallback();
		}else if(oRoot){
			var oXSL = oRoot.loadFromCache(aXSLs[t]);
			var oProc = new XSLTProcessor();
    		oProc.importStylesheet(oXSL);

			if (aParams != null)
				for (var i = 0; i < (aParams.length /2); i++) {
					oProc.setParameter(null, aParams[i *2], aParams[i *2 +1]);
				}

			if (t < aXSLs.length -1)
				oXML = oProc.transformToDocument(oXML);
			else
				oXML = oProc.transformToFragment(oXML, document);
			processXSLT_Async(oXML,aXSLs,aParams,t+1,fCallback);
		}else{
			var oHTTPRequest = new XMLHttpRequest();
	    	oHTTPRequest.open('GET', aXSLs[t], true);
	    	oHTTPRequest.onload = function(e)
    		{
    			 if (oHTTPRequest.readyState === 4) {
				    if (oHTTPRequest.status != 200) {
				      alert(oHTTPRequest.statusText);
				      return;
				    }
  				}else{
  					return;
  				}
	    		var oXSL = oHTTPRequest.responseXML;
	    		var oProc = new XSLTProcessor();
	    		oProc.importStylesheet(oXSL);

				if (aParams != null)
					for (var i = 0; i < (aParams.length /2); i++) {
						oProc.setParameter(null, aParams[i *2], aParams[i *2 +1]);
					}

				if (t < aXSLs.length -1)
					oXML = oProc.transformToDocument(oXML);
				else
					oXML = oProc.transformToFragment(oXML, document);
				processXSLT_Async(oXML,aXSLs,aParams,t+1,fCallback);
			};
			oHTTPRequest.onerror = function(e){
				alert(e);
			}
    		oHTTPRequest.send(null);
    	}
	}
	
		function transformDataNS_Async(aXSLs, sXML, aParams, fCallback) {
    		var oHTTPRequest = new XMLHttpRequest();
    		oHTTPRequest.open('GET', sXML, true);
    		oHTTPRequest.onload = function(e){
				if (oHTTPRequest.readyState === 4) {
				    if (oHTTPRequest.status != 200) {
				      alert(oHTTPRequest.statusText);
				      return;
				    }
  				}else{
  					return;
  				}    		
	    		var oXML = oHTTPRequest.responseXML;
	    		processXSLT_Async(oXML,aXSLs, aParams,0,fCallback)
				}
			oHTTPRequest.onerror = function(e){
				alert(e);
			}

    		oHTTPRequest.send(null);

		}




    	function doRender() {
    		var aTransParams = new Array();
    		var aXSLs = new Array();
    		var sXML = '';

			var sURL = window.location.href;
			var iPos = sURL.indexOf('?') +1;
			//var sHTTPRoot = sURL.substring(0, iPos);
			//sHTTPRoot = sHTTPRoot.substring(0, sHTTPRoot.lastIndexOf('/') +1);
			sHTTPRoot = './';

			sURL = sURL.substr(iPos);
			var aParams = sURL.split('&');
			var aParam;
			for (var i = 0; i < aParams.length; i++) {
				aParam = aParams[i].split('=');
				switch (aParam[0]) {
					case 'xml':
						sXML = sHTTPRoot +aParam[1];
						break;
					case 'cxsl':
						aXSLs.push(sHTTPRoot +aParam[1]);
						break;
					default:
						if (aParam[0].substr(0, 3) == 'xsl')
							aXSLs.push(sHTTPRoot +aParam[1]);
						else
							aTransParams.push(String(aParam[0]), decodeURI(String(aParam[1])));

						break;
				}
			}

			aTransParams.push('body', 'no');
			var fCallback = function(){
				// Hilight
		    	var sHash = getParam('hash');
				var sHitSet = getParam('hitset');
				if (sHitSet != '')
					document.body.innerHTML = hilightWords(document.body.innerHTML, sHitSet, sHash);
				else {
					var oRoot = getRoot();
					if(oRoot!=null){
						var aHits = oRoot.aLastSearchResults;
						if (aHits.length != 0)
							document.body.innerHTML = hilightWords2(document.body.innerHTML, aHits, sHash);
					}
				}

		    	const regex = new RegExp('\^id\\d+\(_\\d+\)\{0,3\}\$');
	    		if (regex.test(sHash))
		    		setTimeout('timedScroll("' +sHash +'")', 100);
	    	};
	    	try{
	    		transformDataIE(aXSLs, sXML, aTransParams,fCallback);
	    	}
	    	catch(e1){
	    	
	    		try 
	    		{
		    		transformDataNS_Async(aXSLs, sXML, aTransParams,fCallback);
		    	} 
		    	
		    	catch(e2) {
		    		//window.location.href = '../error.htm?&e2=' +encodeURIComponent(e2.message);
		    		alert(e2);
		    		return;
		    	}
		    	
	    	}

			
    	}

    </script>

	<body class="render" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" style="PADDING-RIGHT: 0px; PADDING-LEFT: 0px; MARGIN: 0px; overflow: hidden; height: 100%; width: 100%" onload="javascript:doRender();">
		<div id="idTarget"></div>
	</body>
</html>
