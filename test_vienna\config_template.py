#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vienna爬虫数据库配置模板
请复制此文件为 config.py 并修改数据库配置
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': '*********',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'yuxm',            # 数据库用户名
    'password': 'G$jeuwCsk92E@7JZ^P',            # 数据库密码
    'database': 'db_trademark_staging',  # 数据库名称
    'charset': 'utf8mb4'       # 字符集
}

# 爬虫配置
SPIDER_CONFIG = {
    'version': '10',           # Vienna分类版本号
    'clear_before_run': False, # 运行前是否清理现有数据
    'batch_size': 1000,       # 批量插入大小
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',          # 日志级别: DEBUG, INFO, WARNING, ERROR
    'file': 'vienna_spider.log',  # 日志文件名
    'console': True,          # 是否输出到控制台
}
