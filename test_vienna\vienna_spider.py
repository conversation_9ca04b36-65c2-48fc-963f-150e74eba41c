#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vienna商标分类数据清洗工具 - Vienna分类数据爬取和入库
支持从WIPO官网获取1-29类Vienna分类数据并清洗入库
"""

import logging
import pymysql
import re
import os
from typing import List, Dict, Any
from dataclasses import dataclass


def load_config():
    """
    加载配置文件
    优先级：config.py > 默认配置
    """
    try:
        # 尝试导入config.py配置文件
        import config
        db_config = DatabaseConfig(
            host=config.DATABASE_CONFIG['host'],
            port=config.DATABASE_CONFIG['port'],
            user=config.DATABASE_CONFIG['user'],
            password=config.DATABASE_CONFIG['password'],
            database=config.DATABASE_CONFIG['database'],
            charset=config.DATABASE_CONFIG.get('charset', 'utf8mb4')
        )
        logger.info("使用config.py配置文件")
        return db_config
    except ImportError:
        # 如果没有config.py，使用默认配置
        logger.info("未找到config.py，使用默认配置")
        return DatabaseConfig()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vienna_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 移除了网络重试装饰器，因为我们直接解析本地XML文件


@dataclass
class ViennaClassItem:
    """Vienna分类数据项"""
    class_type: str = "VIENNA"  # 固定为VIENNA
    class_id: str = ""  # 分类ID，如 1.1, 1.1.15
    class_level_1: str = ""  # 一级分类，如 1
    class_description_en: str = ""  # 英文描述
    class_description_cn: str = ""  # 中文描述（暂时为空）
    version: str = "10"  # 固定版本号10


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "*********"
    port: int = 3306
    user: str = "yuxm"
    password: str = "G$jeuwCsk92E@7JZ^P"
    database: str = "db_trademark_staging"
    charset: str = "utf8mb4"


class ViennaSpider:
    """Vienna分类数据爬虫"""

    def __init__(self, db_config: DatabaseConfig):
        """
        初始化爬虫

        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.class_range = range(1, 30)  # Vienna分类范围1-29
        self.version = "10"
        self._db_connection = None

    def get_db_connection(self):
        """获取数据库连接"""
        if self._db_connection is None or not self._db_connection.open:
            self._db_connection = pymysql.connect(
                host=self.db_config.host,
                port=self.db_config.port,
                user=self.db_config.user,
                password=self.db_config.password,
                database=self.db_config.database,
                charset=self.db_config.charset,
                autocommit=True
            )
        return self._db_connection

    def close_db_connection(self):
        """关闭数据库连接"""
        if self._db_connection and self._db_connection.open:
            self._db_connection.close()
            self._db_connection = None

    def parse_xml_data(self) -> List[ViennaClassItem]:
        """
        解析XML文件获取Vienna分类数据

        Returns:
            Vienna分类数据项列表
        """
        try:
            xml_file_path = os.path.join(os.path.dirname(__file__), 'full.xml')
            if not os.path.exists(xml_file_path):
                logger.error(f"XML文件不存在: {xml_file_path}")
                return []

            logger.info(f"开始解析XML文件: {xml_file_path}")

            # 解析XML文件
            from xml.etree import ElementTree as ET
            tree = ET.parse(xml_file_path)
            root = tree.getroot()

            items = []

            # 遍历所有category（一级分类）
            for category in root.findall('category'):
                category_id = category.get('id')
                category_text = category.get('text', '')

                logger.info(f"处理一级分类: {category_id} - {category_text}")

                # 遍历division（二级分类）
                for division in category.findall('division'):
                    division_id = division.get('id')
                    division_text = division.get('text', '')

                    # 构建二级分类ID
                    class_id_level2 = f"{category_id}.{division_id}"

                    # 添加二级分类
                    item = ViennaClassItem(
                        class_id=class_id_level2,
                        class_level_1=category_id,
                        class_description_en=division_text,
                        class_description_cn="",  # 暂时为空
                        version=self.version
                    )
                    items.append(item)

                    # 遍历section（三级分类）
                    for section in division.findall('section'):
                        section_id = section.get('id')
                        section_text = section.text or ""

                        # 构建三级分类ID
                        class_id_level3 = f"{category_id}.{division_id}.{section_id}"

                        # 添加三级分类
                        item = ViennaClassItem(
                            class_id=class_id_level3,
                            class_level_1=category_id,
                            class_description_en=section_text,
                            class_description_cn="",  # 暂时为空
                            version=self.version
                        )
                        items.append(item)

                    # 遍历auxiliaries中的auxiliary（辅助分类）
                    auxiliaries = division.find('auxiliaries')
                    if auxiliaries is not None:
                        for auxiliary in auxiliaries.findall('auxiliary'):
                            auxiliary_id = auxiliary.get('id')
                            auxiliary_text = auxiliary.text or ""

                            # 清理auxiliary_text中的note标签内容
                            auxiliary_text = self._clean_auxiliary_text(auxiliary_text)

                            # 构建辅助分类ID
                            class_id_auxiliary = f"{category_id}.{division_id}.{auxiliary_id}"

                            # 添加辅助分类
                            item = ViennaClassItem(
                                class_id=class_id_auxiliary,
                                class_level_1=category_id,
                                class_description_en=auxiliary_text,
                                class_description_cn="",  # 暂时为空
                                version=self.version
                            )
                            items.append(item)

            logger.info(f"XML解析完成，共获取 {len(items)} 个分类项")
            return items

        except Exception as e:
            logger.error(f"解析XML文件失败: {e}")
            return []

    def _clean_auxiliary_text(self, text: str) -> str:
        """
        清理auxiliary文本中的note标签等内容

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 移除note标签及其内容
        import re
        # 移除<note>...</note>标签
        text = re.sub(r'<note[^>]*>.*?</note>', '', text, flags=re.DOTALL)
        # 移除其他可能的HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def save_to_database(self, items: List[ViennaClassItem]) -> bool:
        """
        批量保存数据到数据库

        Args:
            items: Vienna分类数据项列表

        Returns:
            保存是否成功
        """
        if not items:
            logger.warning("没有数据需要保存")
            return True

        cursor = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # 准备SQL语句 - 使用ON DUPLICATE KEY UPDATE处理重复数据
            sql = """
            INSERT INTO trademark_class_map
            (class_type, class_id, class_level_1, class_description_en, class_description_cn, version)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                class_type = VALUES(class_type),
                class_level_1 = VALUES(class_level_1),
                class_description_en = VALUES(class_description_en),
                class_description_cn = VALUES(class_description_cn),
                update_time = CURRENT_TIMESTAMP
            """

            # 准备批量数据
            batch_data = []
            for item in items:
                batch_data.append((
                    item.class_type,
                    item.class_id,
                    item.class_level_1,
                    item.class_description_en,
                    item.class_description_cn,
                    item.version
                ))

            # 批量执行插入
            cursor.executemany(sql, batch_data)

            logger.info(f"成功保存 {len(items)} 条Vienna分类数据到数据库")
            return True

        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()

    def clear_version_data(self) -> bool:
        """
        清理指定版本的数据

        Returns:
            清理是否成功
        """
        cursor = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            sql = "DELETE FROM trademark_class_map WHERE class_type = 'VIENNA' AND version = %s"
            cursor.execute(sql, (self.version,))

            deleted_count = cursor.rowcount
            logger.info(f"清理了 {deleted_count} 条Vienna分类数据（版本: {self.version}）")
            return True

        except Exception as e:
            logger.error(f"清理数据失败: {e}")
            return False
        finally:
            if cursor:
                cursor.close()

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息

        Returns:
            统计信息字典
        """
        cursor = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # 总记录数
            cursor.execute(
                "SELECT COUNT(*) FROM trademark_class_map WHERE class_type = 'VIENNA'"
            )
            total_count = cursor.fetchone()[0]

            # 按版本统计
            cursor.execute("""
                SELECT version, COUNT(*)
                FROM trademark_class_map
                WHERE class_type = 'VIENNA'
                GROUP BY version
            """)
            by_version = dict(cursor.fetchall())

            # 按层级统计
            cursor.execute("""
                SELECT
                    CASE
                        WHEN class_id REGEXP '^[0-9]+\\.[0-9]+\\.[0-9]+$' THEN '三级分类'
                        WHEN class_id REGEXP '^[0-9]+\\.[0-9]+$' THEN '二级分类'
                        ELSE '其他'
                    END as level_type,
                    COUNT(*) as count
                FROM trademark_class_map
                WHERE class_type = 'VIENNA'
                GROUP BY level_type
            """)
            by_level = dict(cursor.fetchall())

            return {
                'total_count': total_count,
                'by_version': by_version,
                'by_level': by_level
            }

        except Exception as e:
            logger.error(f"获取统计信息时发生错误: {e}")
            return {}
        finally:
            if cursor:
                cursor.close()

    def run_spider(self, clear_before: bool = False) -> bool:
        """
        运行爬虫，解析XML并保存所有分类数据

        Args:
            clear_before: 是否在开始前清理现有数据

        Returns:
            爬取是否成功
        """
        try:
            logger.info("开始运行Vienna分类数据爬虫...")

            # 清理现有数据
            if clear_before:
                logger.info("清理现有数据...")
                self.clear_version_data()

            # 解析XML数据
            items = self.parse_xml_data()

            if not items:
                logger.error("没有解析到任何数据")
                return False

            # 保存到数据库
            success = self.save_to_database(items)

            if success:
                logger.info(f"Vienna分类数据爬虫运行成功！共处理 {len(items)} 条数据")

                # 显示统计信息
                stats = self.get_statistics()
                if stats:
                    logger.info(f"数据库统计信息:")
                    logger.info(f"  总记录数: {stats.get('total_count', 0)}")
                    logger.info(f"  按版本统计: {stats.get('by_version', {})}")
                    logger.info(f"  按层级统计: {stats.get('by_level', {})}")

                return True
            else:
                logger.error("保存数据到数据库失败")
                return False

        except Exception as e:
            logger.error(f"运行爬虫时发生错误: {e}")
            return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Vienna商标分类数据爬虫')
    parser.add_argument('--clear', action='store_true', help='清理现有数据后重新爬取')
    parser.add_argument('--stats', action='store_true', help='仅显示统计信息')

    args = parser.parse_args()

    # 加载数据库配置
    db_config = load_config()

    # 创建爬虫实例
    spider = ViennaSpider(db_config)

    try:
        # 仅显示统计信息
        if args.stats:
            stats = spider.get_statistics()
            if stats:
                print("\n数据库统计信息:")
                print(f"总记录数: {stats.get('total_count', 0)}")
                print(f"按版本统计: {stats.get('by_version', {})}")
                print(f"按层级统计: {stats.get('by_level', {})}")
            else:
                print("获取统计信息失败")
            return

        # 运行爬虫
        success = spider.run_spider(clear_before=args.clear)

        if success:
            logger.info("Vienna爬虫运行成功完成！")
        else:
            logger.error("Vienna爬虫运行过程中出现错误")

    except KeyboardInterrupt:
        logger.info("用户中断了爬虫运行")
    except Exception as e:
        logger.error(f"运行爬虫时发生未知错误: {e}")
    finally:
        spider.close_db_connection()


if __name__ == "__main__":
    main()