import requests

# cookies = {
#     'wipo_language': 'en',
#     '_pk_id.5.ec75': '35af34c9def18ab7.1753258249.',
#     '_pk_uid': '0%3DMzVhZjM0YzlkZWYxOGFiNw%3D%3D',
#     '_pk_id.9.ec75': 'f889b36bdb3697d1.1753265652.',
#     'wipo-visitor-uunid': '1e1e4d01fee05c79',
#     '_ga': 'GA1.1.998453335.1753341091',
#     '_pk_ses.9.ec75': '1',
#     '_ga_23MMQ1ZPYM': 'GS2.1.s1753347366$o2$g0$t1753347366$j60$l0$h0',
#     'cebs': '1',
#     '_pk_id.14.ec75': '081970e46185aa64.1753347407.',
#     '_ce.clock_data': '-171%2C1.36.223.86%2C1%2C7ddeda88d0c599cc494da0dece6554d5%2CChrome%2CHK',
#     '_ga_V7SSWS892V': 'GS2.1.s1753347521$o7$g0$t1753347521$j60$l0$h0',
#     '_pk_ref.5.ec75': '%5B%22%22%2C%22%22%2C1753349123%2C%22https%3A%2F%2Fwww.bing.com%2F%22%5D',
#     'ABIW': 'balancer.cms31',
#     '_pk_ref.14.ec75': '%5B%22%22%2C%22%22%2C1753352478%2C%22https%3A%2F%2Fnivilo.wipo.int%2F%22%5D',
#     '_pk_ses.14.ec75': '1',
#     'cebsp_': '5',
#     'wipo_language': 'en',
#     '_ce.s': 'lcw~1753352508221~v11ls~f0c848e0-6877-11f0-b367-89d9248ff8d2~v~cac3a83a4974561ee87ee0381106a6c9f8d0f411~vir~returning~lva~1753352481619~vpv~0~v11.cs~411929~v11.s~f0c848e0-6877-11f0-b367-89d9248ff8d2~v11.vs~cac3a83a4974561ee87ee0381106a6c9f8d0f411~v11.fsvd~eyJ1cmwiOiJ3aXBvLmludC9lbi93ZWIvY2xhc3NpZmljYXRpb24tdmllbm5hIiwicmVmIjoiaHR0cHM6Ly9uaXZpbG8ud2lwby5pbnQvIiwidXRtIjpbXX0%3D~v11.sla~1753352479871~gtrk.la~mdh8tb1d~lcw~1753352509729',
#     '_ga_15TSHJ0HWP': 'GS2.1.s1753352479$o5$g0$t1753352509$j30$l0$h0',
#     '_ga_67EB70XWBQ': 'GS2.1.s1753352474$o2$g1$t1753352520$j14$l0$h0',
# }

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-TW,zh-CN;q=0.9,zh;q=0.8',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=0, i',
    'referer': 'https://nivilo.wipo.int/vienna10/index.htm?lang=EN',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'iframe',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}

response = requests.get(
    'https://nivilo.wipo.int/vienna10/divrender.htm?xml=xml/en/full.xml&xsl=xslt/addmode.xsl&cxsl=xslt/vienna8.xsl&mode=full&index=1&lang=EN&hash=id1',
    headers=headers,
)

print(response.status_code)

with open(r'E:\Pycharm\uptook-data-schema\test_vienna\test.html', 'w', encoding='utf-8') as f:
    f.write(response.text)
