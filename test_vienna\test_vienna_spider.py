#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vienna爬虫测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from vienna_spider import ViennaSpider, DatabaseConfig


def test_xml_parsing():
    """测试XML解析功能"""
    print("=" * 50)
    print("测试XML解析功能")
    print("=" * 50)
    
    # 创建配置（使用默认配置，不连接数据库）
    db_config = DatabaseConfig()
    spider = ViennaSpider(db_config)
    
    # 测试解析功能
    items = spider.parse_xml_data()
    
    if items:
        print(f"✅ 成功解析XML数据，共 {len(items)} 个分类项")
        
        # 显示前10个项目
        print("\n前10个分类项:")
        for i, item in enumerate(items[:10]):
            print(f"{i+1}. {item.class_id}: {item.class_description_en}")
        
        # 显示不同层级的统计
        level2_count = len([item for item in items if len(item.class_id.split('.')) == 2])
        level3_count = len([item for item in items if len(item.class_id.split('.')) == 3])
        
        print(f"\n层级统计:")
        print(f"二级分类: {level2_count} 个")
        print(f"三级分类: {level3_count} 个")
        print(f"总计: {len(items)} 个")
        
        return True
    else:
        print("❌ 解析XML数据失败")
        return False


def test_database_connection():
    """测试数据库连接"""
    print("\n" + "=" * 50)
    print("测试数据库连接")
    print("=" * 50)
    
    # 这里需要用户提供真实的数据库配置
    print("请提供数据库配置信息（测试连接用）:")
    host = input("数据库主机 [localhost]: ").strip() or "localhost"
    port = int(input("数据库端口 [3306]: ").strip() or "3306")
    user = input("数据库用户名 [root]: ").strip() or "root"
    password = input("数据库密码: ").strip()
    database = input("数据库名 [db_trademark_staging]: ").strip() or "db_trademark_staging"
    
    db_config = DatabaseConfig(
        host=host,
        port=port,
        user=user,
        password=password,
        database=database
    )
    
    spider = ViennaSpider(db_config)
    
    try:
        # 测试连接
        conn = spider.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print("✅ 数据库连接成功")
            
            # 测试表是否存在
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = 'trademark_class_map'
            """, (database,))
            table_exists = cursor.fetchone()[0] > 0
            cursor.close()
            
            if table_exists:
                print("✅ 表 trademark_class_map 存在")
                return True
            else:
                print("❌ 表 trademark_class_map 不存在，请先创建表")
                return False
        else:
            print("❌ 数据库连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        spider.close_db_connection()


def test_full_process():
    """测试完整流程（需要数据库）"""
    print("\n" + "=" * 50)
    print("测试完整流程")
    print("=" * 50)
    
    # 获取数据库配置
    print("请提供数据库配置信息:")
    host = input("数据库主机 [localhost]: ").strip() or "localhost"
    port = int(input("数据库端口 [3306]: ").strip() or "3306")
    user = input("数据库用户名 [root]: ").strip() or "root"
    password = input("数据库密码: ").strip()
    database = input("数据库名 [db_trademark_staging]: ").strip() or "db_trademark_staging"
    
    db_config = DatabaseConfig(
        host=host,
        port=port,
        user=user,
        password=password,
        database=database
    )
    
    spider = ViennaSpider(db_config)
    
    try:
        # 运行爬虫（清理现有数据）
        success = spider.run_spider(clear_before=True)
        
        if success:
            print("✅ 完整流程测试成功")
            return True
        else:
            print("❌ 完整流程测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False
    finally:
        spider.close_db_connection()


def main():
    """主函数"""
    print("Vienna爬虫测试脚本")
    print("请选择测试项目:")
    print("1. 仅测试XML解析（无需数据库）")
    print("2. 测试数据库连接")
    print("3. 测试完整流程（解析+入库）")
    
    choice = input("\n请输入选择 [1]: ").strip() or "1"
    
    if choice == "1":
        test_xml_parsing()
    elif choice == "2":
        if test_xml_parsing():
            test_database_connection()
    elif choice == "3":
        if test_xml_parsing():
            if test_database_connection():
                test_full_process()
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
